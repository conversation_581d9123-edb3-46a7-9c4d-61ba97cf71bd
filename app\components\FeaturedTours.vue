<template>
  <section class="py-16 bg-white">
    <div class="container-custom">
      <!-- 标题区域 -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">精选旅游产品</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">精心策划的旅游线路，为您提供难忘的旅行体验</p>
      </div>

      <!-- 产品网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div v-for="tour in tours" :key="tour.id" class="group cursor-pointer" @click="goToTour(tour.slug)">
          <div
            class="card overflow-hidden hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2"
          >
            <!-- 图片区域 -->
            <div class="relative h-56 overflow-hidden">
              <img
                :src="tour.image"
                :alt="tour.title"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

              <!-- 折扣标签 -->
              <div
                v-if="tour.discount"
                class="absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium"
              >
                {{ tour.discount }}折
              </div>

              <!-- 天数标签 -->
              <div class="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ tour.days }}天{{ tour.nights }}晚
              </div>

              <!-- 收藏按钮 -->
              <button
                @click.stop="toggleFavorite(tour.id)"
                class="absolute bottom-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
              >
                <span v-if="tour.isFavorite" class="text-red-500">❤️</span>
                <span v-else>🤍</span>
              </button>
            </div>

            <!-- 内容区域 -->
            <div class="p-6">
              <!-- 标题和评分 -->
              <div class="mb-3">
                <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                  {{ tour.title }}
                </h3>
                <div class="flex items-center space-x-2">
                  <div class="flex items-center space-x-1">
                    <span class="text-yellow-400">⭐</span>
                    <span class="text-sm font-medium">{{ tour.rating }}</span>
                  </div>
                  <span class="text-sm text-gray-500">({{ tour.reviews }}条评论)</span>
                </div>
              </div>

              <!-- 行程亮点 -->
              <div class="mb-4">
                <p class="text-gray-600 text-sm line-clamp-2">
                  {{ tour.highlights }}
                </p>
              </div>

              <!-- 包含服务 -->
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  v-for="service in tour.includes"
                  :key="service"
                  class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full"
                >
                  ✓ {{ service }}
                </span>
              </div>

              <!-- 价格和预订 -->
              <div class="flex items-center justify-between">
                <div class="flex items-baseline space-x-2">
                  <span class="text-2xl font-bold text-primary-600">¥{{ tour.price }}</span>
                  <span v-if="tour.originalPrice" class="text-sm text-gray-500 line-through"
                    >¥{{ tour.originalPrice }}</span
                  >
                  <span class="text-sm text-gray-500">/人</span>
                </div>
                <button @click.stop="bookTour(tour.id)" class="btn-primary px-4 py-2 text-sm">立即预订</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 查看更多按钮 -->
      <div class="text-center mt-12">
        <button class="btn-secondary px-8 py-3 text-lg">查看更多产品</button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  // 旅游产品数据
  const tours = ref([
    {
      id: 1,
      title: '北京故宫+长城+颐和园经典3日游',
      slug: 'beijing-classic-3days',
      image:
        'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 3,
      nights: 2,
      price: 1299,
      originalPrice: 1599,
      discount: 8,
      rating: 4.8,
      reviews: 156,
      highlights: '深度游览故宫博物院，登临万里长城，漫步颐和园皇家园林，品尝正宗北京烤鸭',
      includes: ['酒店住宿', '景点门票', '专业导游', '旅游保险'],
      isFavorite: false,
    },
    {
      id: 2,
      title: '三亚天涯海角+蜈支洲岛浪漫5日游',
      slug: 'sanya-romantic-5days',
      image:
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 5,
      nights: 4,
      price: 2899,
      originalPrice: null,
      discount: null,
      rating: 4.7,
      reviews: 234,
      highlights: '蜈支洲岛水上项目，天涯海角浪漫打卡，亚龙湾海滩度假，热带雨林探险',
      includes: ['海景酒店', '往返机票', '水上项目', '接送服务'],
      isFavorite: true,
    },
    {
      id: 3,
      title: '西安兵马俑+华山+古城墙文化4日游',
      slug: 'xian-culture-4days',
      image:
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 4,
      nights: 3,
      price: 1699,
      originalPrice: 1999,
      discount: 8.5,
      rating: 4.9,
      reviews: 189,
      highlights: '世界第八大奇迹兵马俑，华山论剑登顶体验，古城墙骑行，回民街美食',
      includes: ['精品酒店', '景点门票', '文化讲解', '特色餐食'],
      isFavorite: false,
    },
    {
      id: 4,
      title: '杭州西湖+乌镇+千岛湖江南6日游',
      slug: 'hangzhou-jiangnan-6days',
      image:
        'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 6,
      nights: 5,
      price: 2199,
      originalPrice: null,
      discount: null,
      rating: 4.6,
      reviews: 167,
      highlights: '西湖十景游船，乌镇水乡古镇，千岛湖游船观光，品茶文化体验',
      includes: ['园林酒店', '游船票', '茶艺体验', '江南美食'],
      isFavorite: false,
    },
    {
      id: 5,
      title: '成都熊猫基地+九寨沟+黄龙7日游',
      slug: 'chengdu-jiuzhaigou-7days',
      image:
        'https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 7,
      nights: 6,
      price: 3299,
      originalPrice: 3899,
      discount: 8.5,
      rating: 4.8,
      reviews: 298,
      highlights: '大熊猫基地亲密接触，九寨沟童话世界，黄龙钙化池奇观，川菜美食之旅',
      includes: ['舒适酒店', '景区门票', '往返交通', '川菜体验'],
      isFavorite: true,
    },
    {
      id: 6,
      title: '丽江古城+玉龙雪山+泸沽湖5日游',
      slug: 'lijiang-yulong-5days',
      image:
        'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      days: 5,
      nights: 4,
      price: 2599,
      originalPrice: null,
      discount: null,
      rating: 4.7,
      reviews: 145,
      highlights: '丽江古城漫步，玉龙雪山索道，泸沽湖女儿国，纳西文化体验',
      includes: ['特色客栈', '索道票', '文化表演', '民族餐食'],
      isFavorite: false,
    },
  ]);

  // 方法
  const goToTour = (slug: string) => {
    navigateTo(`/tours/${slug}`);
  };

  const toggleFavorite = (tourId: number) => {
    const tour = tours.value.find((t) => t.id === tourId);
    if (tour) {
      tour.isFavorite = !tour.isFavorite;
      ElMessage.success(tour.isFavorite ? '已添加到收藏' : '已取消收藏');
    }
  };

  const bookTour = (tourId: number) => {
    ElMessage.success('正在跳转到预订页面...');
    // 跳转到预订页面
    navigateTo(`/booking/${tourId}`);
  };
</script>

<style scoped>
  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
