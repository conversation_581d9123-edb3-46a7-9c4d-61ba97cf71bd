<template>
  <section class="py-16 bg-gray-50">
    <div class="container-custom">
      <!-- Title area -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Popular Destinations</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Explore exciting attractions around the world and discover your perfect journey
        </p>
      </div>

      <!-- Destinations grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="destination in destinations"
          :key="destination.id"
          class="group cursor-pointer"
          @click="goToDestination(destination.slug)"
        >
          <div
            class="card overflow-hidden hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2"
          >
            <!-- Image area -->
            <div class="relative h-64 overflow-hidden">
              <img
                :src="destination.image"
                :alt="destination.name"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

              <!-- Price tag -->
              <div class="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                ${{ destination.price }} from
              </div>

              <!-- Destination name -->
              <div class="absolute bottom-4 left-4 text-white">
                <h3 class="text-xl font-bold">{{ destination.name }}</h3>
                <p class="text-sm text-gray-200">{{ destination.country }}</p>
              </div>
            </div>

            <!-- Content area -->
            <div class="p-6">
              <p class="text-gray-600 mb-4 line-clamp-2">
                {{ destination.description }}
              </p>

              <!-- Feature tags -->
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  v-for="tag in destination.tags"
                  :key="tag"
                  class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  {{ tag }}
                </span>
              </div>

              <!-- Rating and reviews -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-1">
                  <span class="text-yellow-400">⭐</span>
                  <span class="text-sm font-medium">{{ destination.rating }}</span>
                  <span class="text-sm text-gray-500">({{ destination.reviews }} reviews)</span>
                </div>
                <button class="text-primary-600 hover:text-primary-700 font-medium text-sm">View Details →</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- View more button -->
      <div class="text-center mt-12">
        <button class="btn-primary px-8 py-3 text-lg">View More Destinations</button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  // Destinations data
  const destinations = [
    {
      id: 1,
      name: 'Beijing',
      country: 'China',
      slug: 'beijing',
      image:
        'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description:
        'Perfect fusion of ancient imperial city and modern metropolis, explore world heritage sites like the Forbidden City and Great Wall.',
      price: 199,
      rating: 4.8,
      reviews: 2156,
      tags: ['History & Culture', 'World Heritage', 'Cuisine'],
    },
    {
      id: 2,
      name: 'Sanya',
      country: 'China',
      slug: 'sanya',
      image:
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description:
        'Tropical seaside resort destination with coconut breeze and ocean charm, sunshine and beaches - the perfect choice for relaxation.',
      price: 299,
      rating: 4.7,
      reviews: 1834,
      tags: ['Beach Resort', 'Tropical', 'Diving'],
    },
    {
      id: 3,
      name: "Xi'an",
      country: 'China',
      slug: 'xian',
      image:
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description:
        'Ancient capital of thirteen dynasties, home of the Terracotta Warriors, experience the weight and glory of thousand-year history.',
      price: 149,
      rating: 4.9,
      reviews: 3021,
      tags: ['Ancient Capital', 'Terracotta Warriors', 'History'],
    },
    {
      id: 4,
      name: 'Hangzhou',
      country: 'China',
      slug: 'hangzhou',
      image:
        'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description:
        'Paradise on earth, West Lake scenery like poetry and painting, typical representative of Jiangnan water towns.',
      price: 129,
      rating: 4.6,
      reviews: 1567,
      tags: ['Water Town', 'West Lake', 'Gardens'],
    },
    {
      id: 5,
      name: 'Chengdu',
      country: 'China',
      slug: 'chengdu',
      image:
        'https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: 'Land of abundance, panda hometown, birthplace of hotpot culture, exemplar of slow living.',
      price: 169,
      rating: 4.8,
      reviews: 2234,
      tags: ['Pandas', 'Hotpot', 'Slow Living'],
    },
    {
      id: 6,
      name: 'Lijiang',
      country: 'China',
      slug: 'lijiang',
      image:
        'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description:
        'Ancient town charm surrounded by snow mountains, treasure of Naxi culture, pilgrimage site for literary youth.',
      price: 239,
      rating: 4.7,
      reviews: 1789,
      tags: ['Ancient Town', 'Snow Mountains', 'Literary'],
    },
  ];

  // Methods
  const goToDestination = (slug: string) => {
    // Navigate to destination details page
    navigateTo(`/destinations/${slug}`);
  };
</script>

<style scoped>
  /* Text truncation styles */
  .line-clamp-2 {
    display: -webkit-box;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
