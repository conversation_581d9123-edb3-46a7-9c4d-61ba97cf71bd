<template>
  <section class="py-16 bg-gray-50">
    <div class="container-custom">
      <!-- 标题区域 -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">热门目的地</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">探索世界各地的精彩景点，发现属于你的完美旅程</p>
      </div>

      <!-- 目的地网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div
          v-for="destination in destinations"
          :key="destination.id"
          class="group cursor-pointer"
          @click="goToDestination(destination.slug)"
        >
          <div
            class="card overflow-hidden hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2"
          >
            <!-- 图片区域 -->
            <div class="relative h-64 overflow-hidden">
              <img
                :src="destination.image"
                :alt="destination.name"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

              <!-- 价格标签 -->
              <div class="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                ¥{{ destination.price }}起
              </div>

              <!-- 目的地名称 -->
              <div class="absolute bottom-4 left-4 text-white">
                <h3 class="text-xl font-bold">{{ destination.name }}</h3>
                <p class="text-sm text-gray-200">{{ destination.country }}</p>
              </div>
            </div>

            <!-- 内容区域 -->
            <div class="p-6">
              <p class="text-gray-600 mb-4 line-clamp-2">
                {{ destination.description }}
              </p>

              <!-- 特色标签 -->
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  v-for="tag in destination.tags"
                  :key="tag"
                  class="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  {{ tag }}
                </span>
              </div>

              <!-- 评分和评论 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-1">
                  <span class="text-yellow-400">⭐</span>
                  <span class="text-sm font-medium">{{ destination.rating }}</span>
                  <span class="text-sm text-gray-500">({{ destination.reviews }}条评论)</span>
                </div>
                <button class="text-primary-600 hover:text-primary-700 font-medium text-sm">查看详情 →</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 查看更多按钮 -->
      <div class="text-center mt-12">
        <button class="btn-primary px-8 py-3 text-lg">查看更多目的地</button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  // 目的地数据
  const destinations = [
    {
      id: 1,
      name: '北京',
      country: '中国',
      slug: 'beijing',
      image:
        'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '古老的皇城与现代都市完美融合，故宫、长城等世界文化遗产等你探索。',
      price: 1299,
      rating: 4.8,
      reviews: 2156,
      tags: ['历史文化', '世界遗产', '美食'],
    },
    {
      id: 2,
      name: '三亚',
      country: '中国',
      slug: 'sanya',
      image:
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '热带海滨度假胜地，椰风海韵，阳光沙滩，是放松身心的完美选择。',
      price: 2199,
      rating: 4.7,
      reviews: 1834,
      tags: ['海滨度假', '热带风情', '潜水'],
    },
    {
      id: 3,
      name: '西安',
      country: '中国',
      slug: 'xian',
      image:
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '十三朝古都，兵马俑的故乡，感受千年历史的厚重与辉煌。',
      price: 999,
      rating: 4.9,
      reviews: 3021,
      tags: ['古都', '兵马俑', '历史'],
    },
    {
      id: 4,
      name: '杭州',
      country: '中国',
      slug: 'hangzhou',
      image:
        'https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '人间天堂，西湖美景如诗如画，江南水乡的典型代表。',
      price: 899,
      rating: 4.6,
      reviews: 1567,
      tags: ['江南水乡', '西湖', '园林'],
    },
    {
      id: 5,
      name: '成都',
      country: '中国',
      slug: 'chengdu',
      image:
        'https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '天府之国，熊猫故乡，火锅文化的发源地，慢生活的典范。',
      price: 1099,
      rating: 4.8,
      reviews: 2234,
      tags: ['熊猫', '火锅', '慢生活'],
    },
    {
      id: 6,
      name: '丽江',
      country: '中国',
      slug: 'lijiang',
      image:
        'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: '古城韵味，雪山环绕，纳西文化的瑰宝，文艺青年的朝圣地。',
      price: 1599,
      rating: 4.7,
      reviews: 1789,
      tags: ['古城', '雪山', '文艺'],
    },
  ];

  // 方法
  const goToDestination = (slug: string) => {
    // 跳转到目的地详情页
    navigateTo(`/destinations/${slug}`);
  };
</script>

<style scoped>
  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
