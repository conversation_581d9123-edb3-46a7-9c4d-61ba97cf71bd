<template>
  <footer class="bg-gray-900 text-white">
    <!-- Main content area -->
    <div class="container-custom py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company information -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold">T</div>
            <span class="text-xl font-bold">Travel</span>
          </div>
          <p class="text-gray-300 text-sm leading-relaxed">
            Professional travel service platform dedicated to providing the best travel experience for every traveler.
            Let's explore the beauty of the world together!
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 📘 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 🐦 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 📷 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 💼 </a>
          </div>
        </div>

        <!-- Quick links -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Quick Links</h3>
          <ul class="space-y-2">
            <li v-for="link in quickLinks" :key="link.name">
              <NuxtLink :to="link.path" class="text-gray-300 hover:text-white transition-colors duration-200 text-sm">
                {{ link.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Popular destinations -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Popular Destinations</h3>
          <ul class="space-y-2">
            <li v-for="destination in popularDestinations" :key="destination.name">
              <NuxtLink
                :to="destination.path"
                class="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
              >
                {{ destination.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Contact information -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">Contact Us</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <span class="text-primary-400 mt-1">📍</span>
              <div class="text-gray-300 text-sm">
                <p>123 Travel Street</p>
                <p>New York, NY 10001</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">📞</span>
              <span class="text-gray-300 text-sm">+1-800-123-4567</span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">✉️</span>
              <span class="text-gray-300 text-sm"><EMAIL></span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">🕒</span>
              <span class="text-gray-300 text-sm">Mon-Sun 9:00-18:00</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Divider -->
    <div class="border-t border-gray-800" />

    <!-- Bottom copyright information -->
    <div class="container-custom py-6">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <div class="text-gray-400 text-sm">© {{ currentYear }} Travel. All rights reserved.</div>
        <div class="flex flex-wrap items-center space-x-6 text-sm">
          <NuxtLink
            v-for="legal in legalLinks"
            :key="legal.name"
            :to="legal.path"
            class="text-gray-400 hover:text-white transition-colors duration-200"
          >
            {{ legal.name }}
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Back to top button -->
    <button
      v-show="showBackToTop"
      class="fixed bottom-8 right-8 w-12 h-12 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-all duration-200 flex items-center justify-center z-40"
      @click="scrollToTop"
    >
      ⬆️
    </button>
  </footer>
</template>

<script setup lang="ts">
  // Reactive data
  const showBackToTop = ref(false);
  const currentYear = new Date().getFullYear();

  // Quick links
  const quickLinks = [
    { name: 'About Us', path: '/about' },
    { name: 'Contact Us', path: '/contact' },
    { name: 'Help Center', path: '/help' },
    { name: 'FAQ', path: '/faq' },
    { name: 'Booking Terms', path: '/booking-terms' },
    { name: 'Cancellation Policy', path: '/cancellation' },
  ];

  // Popular destinations
  const popularDestinations = [
    { name: 'Beijing', path: '/destinations/beijing' },
    { name: 'Shanghai', path: '/destinations/shanghai' },
    { name: "Xi'an", path: '/destinations/xian' },
    { name: 'Chengdu', path: '/destinations/chengdu' },
    { name: 'Hangzhou', path: '/destinations/hangzhou' },
    { name: 'Sanya', path: '/destinations/sanya' },
  ];

  // Legal links
  const legalLinks = [
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Terms of Service', path: '/terms' },
    { name: 'Site Map', path: '/sitemap' },
  ];

  // Methods
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // Listen to scroll events
  onMounted(() => {
    const handleScroll = () => {
      showBackToTop.value = window.scrollY > 300;
    };

    window.addEventListener('scroll', handleScroll);

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll);
    });
  });
</script>

<style scoped>
  /* Custom styles */
  .router-link-active {
    color: #ffffff;
  }
</style>
