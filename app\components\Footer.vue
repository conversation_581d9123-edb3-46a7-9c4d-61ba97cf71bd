<template>
  <footer class="bg-gray-900 text-white">
    <!-- 主要内容区域 -->
    <div class="container-custom py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 公司信息 -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold">T</div>
            <span class="text-xl font-bold">Travel</span>
          </div>
          <p class="text-gray-300 text-sm leading-relaxed">
            专业的旅游服务平台，致力于为每一位旅行者提供最优质的旅游体验。让我们一起探索世界的美好！
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 📘 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 🐦 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 📷 </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors duration-200"> 💼 </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">快速链接</h3>
          <ul class="space-y-2">
            <li v-for="link in quickLinks" :key="link.name">
              <NuxtLink :to="link.path" class="text-gray-300 hover:text-white transition-colors duration-200 text-sm">
                {{ link.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 热门目的地 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">热门目的地</h3>
          <ul class="space-y-2">
            <li v-for="destination in popularDestinations" :key="destination.name">
              <NuxtLink
                :to="destination.path"
                class="text-gray-300 hover:text-white transition-colors duration-200 text-sm"
              >
                {{ destination.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 联系信息 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">联系我们</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <span class="text-primary-400 mt-1">📍</span>
              <div class="text-gray-300 text-sm">
                <p>北京市朝阳区</p>
                <p>建国门外大街1号</p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">📞</span>
              <span class="text-gray-300 text-sm">400-123-4567</span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">✉️</span>
              <span class="text-gray-300 text-sm"><EMAIL></span>
            </div>
            <div class="flex items-center space-x-3">
              <span class="text-primary-400">🕒</span>
              <span class="text-gray-300 text-sm">周一至周日 9:00-18:00</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="border-t border-gray-800" />

    <!-- 底部版权信息 -->
    <div class="container-custom py-6">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        <div class="text-gray-400 text-sm">© {{ currentYear }} Travel. 保留所有权利。</div>
        <div class="flex flex-wrap items-center space-x-6 text-sm">
          <NuxtLink
            v-for="legal in legalLinks"
            :key="legal.name"
            :to="legal.path"
            class="text-gray-400 hover:text-white transition-colors duration-200"
          >
            {{ legal.name }}
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button
      v-show="showBackToTop"
      class="fixed bottom-8 right-8 w-12 h-12 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg transition-all duration-200 flex items-center justify-center z-40"
      @click="scrollToTop"
    >
      ⬆️
    </button>
  </footer>
</template>

<script setup lang="ts">
  // 响应式数据
  const showBackToTop = ref(false);
  const currentYear = new Date().getFullYear();

  // 快速链接
  const quickLinks = [
    { name: '关于我们', path: '/about' },
    { name: '联系我们', path: '/contact' },
    { name: '帮助中心', path: '/help' },
    { name: '常见问题', path: '/faq' },
    { name: '预订须知', path: '/booking-terms' },
    { name: '退改政策', path: '/cancellation' },
  ];

  // 热门目的地
  const popularDestinations = [
    { name: '北京', path: '/destinations/beijing' },
    { name: '上海', path: '/destinations/shanghai' },
    { name: '西安', path: '/destinations/xian' },
    { name: '成都', path: '/destinations/chengdu' },
    { name: '杭州', path: '/destinations/hangzhou' },
    { name: '三亚', path: '/destinations/sanya' },
  ];

  // 法律链接
  const legalLinks = [
    { name: '隐私政策', path: '/privacy' },
    { name: '服务条款', path: '/terms' },
    { name: '网站地图', path: '/sitemap' },
  ];

  // 方法
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // 监听滚动事件
  onMounted(() => {
    const handleScroll = () => {
      showBackToTop.value = window.scrollY > 300;
    };

    window.addEventListener('scroll', handleScroll);

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll);
    });
  });
</script>

<style scoped>
  /* 自定义样式 */
  .router-link-active {
    color: #ffffff;
  }
</style>
