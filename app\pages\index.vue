<template>
  <div>
    <!-- Home page carousel -->
    <HeroBanner />

    <!-- Popular destinations -->
    <PopularDestinations />

    <!-- Featured tours -->
    <FeaturedTours />

    <!-- Why choose us -->
    <WhyChooseUs />
  </div>
</template>

<script lang="ts" setup>
  // Import components
  import HeroBanner from '~/components/HeroBanner.vue';
  import PopularDestinations from '~/components/PopularDestinations.vue';
  import FeaturedTours from '~/components/FeaturedTours.vue';
  import WhyChooseUs from '~/components/WhyChooseUs.vue';

  // Set page meta information
  useHead({
    title: 'Home - Travel Website',
    meta: [
      {
        name: 'description',
        content: 'Professional travel service platform providing quality travel products and services',
      },
    ],
  });

  // Set layout
  definePageMeta({
    layout: 'default',
  });
</script>

<style scoped>
  /* Home page specific styles */
</style>
