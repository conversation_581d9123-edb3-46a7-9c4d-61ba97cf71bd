<template>
  <div>
    <!-- 首页轮播图 -->
    <HeroBanner />

    <!-- 热门目的地 -->
    <PopularDestinations />

    <!-- 精选旅游产品 -->
    <FeaturedTours />

    <!-- 为什么选择我们 -->
    <WhyChooseUs />
  </div>
</template>

<script lang="ts" setup>
  // 导入组件
  import HeroBanner from '~/components/HeroBanner.vue';
  import PopularDestinations from '~/components/PopularDestinations.vue';
  import FeaturedTours from '~/components/FeaturedTours.vue';
  import WhyChooseUs from '~/components/WhyChooseUs.vue';

  // 设置页面元信息
  useHead({
    title: '首页 - 旅游网站',
    meta: [{ name: 'description', content: '专业的旅游服务平台，提供优质的旅游产品和服务' }],
  });

  // 设置布局
  definePageMeta({
    layout: 'default',
  });
</script>

<style scoped>
  /* 首页特定样式 */
</style>
