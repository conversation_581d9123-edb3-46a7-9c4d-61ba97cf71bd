<template>
  <el-config-provider :locale="locale">
    <el-scrollbar class="min-h-full" style="height: 100%">
      <div class="min-h-full flex flex-col">
        <!-- Header -->
        <Header />

        <!-- Main content area -->
        <main class="flex-1 h-0">
          <slot />
        </main>

        <!-- Footer -->
        <Footer />
      </div>
    </el-scrollbar>
  </el-config-provider>
</template>

<script setup lang="ts">
  // Import components
  import Header from '../components/Header.vue';
  import Footer from '../components/Footer.vue';
  import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
  import en from 'element-plus/dist/locale/en.mjs';

  const language = ref('en');
  const locale = computed(() => (language.value === 'zh-cn' ? zhCn : en));

  // Set page meta information
  useHead({
    title: 'Travel Website - Discover the Beauty of the World',
    meta: [
      {
        name: 'description',
        content: 'Professional travel service platform providing you with the best travel experience',
      },
      { name: 'keywords', content: 'travel,trip,vacation,attractions,hotels,flights' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    ],
  });
</script>

<style scoped>
  /* Custom scrollbar styles */
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
</style>
