<template>
  <div class="h-full flex flex-col">
    <!-- 使用 el-scrollbar 替代默认滚动条 -->
    <el-scrollbar class="flex-1" style="height: 100%">
      <div class="h-full flex flex-col">
        <!-- 头部 -->
        <Header />

        <!-- 主要内容区域 -->
        <main class="flex-1 h-0">
          <slot />
        </main>

        <!-- 底部 -->
        <Footer />
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
  // 导入组件
  import Header from '../components/Header.vue';
  import Footer from '../components/Footer.vue';

  // 设置页面元信息
  useHead({
    title: '旅游网站 - 探索世界的美好',
    meta: [
      { name: 'description', content: '专业的旅游服务平台，为您提供最优质的旅游体验' },
      { name: 'keywords', content: '旅游,旅行,度假,景点,酒店,机票' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    ],
  });
</script>

<style scoped>
  /* 自定义滚动条样式 */
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
</style>
