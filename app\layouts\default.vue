<template>
  <el-config-provider :locale="locale">
    <el-scrollbar class="min-h-full" style="height: 100%">
      <div class="min-h-full flex flex-col">
        <!-- 头部 -->
        <Header />

        <!-- 主要内容区域 -->
        <main class="flex-1 h-0">
          <slot />
        </main>

        <!-- 底部 -->
        <Footer />
      </div>
    </el-scrollbar>
  </el-config-provider>
</template>

<script setup lang="ts">
  // 导入组件
  import Header from '../components/Header.vue';
  import Footer from '../components/Footer.vue';
  import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
  import en from 'element-plus/dist/locale/en.mjs';

  const language = ref('zh-cn');
  const locale = computed(() => (language.value === 'zh-cn' ? zhCn : en));

  // 设置页面元信息
  useHead({
    title: '旅游网站 - 探索世界的美好',
    meta: [
      { name: 'description', content: '专业的旅游服务平台，为您提供最优质的旅游体验' },
      { name: 'keywords', content: '旅游,旅行,度假,景点,酒店,机票' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    ],
  });
</script>

<style scoped>
  /* 自定义滚动条样式 */
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
</style>
