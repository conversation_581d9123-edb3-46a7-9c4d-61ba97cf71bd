<template>
  <section class="py-16 bg-primary-50">
    <div class="container-custom">
      <!-- 标题区域 -->
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">为什么选择我们</h2>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">专业的服务团队，丰富的旅游经验，为您打造完美的旅行体验</p>
      </div>

      <!-- 特色服务网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <div v-for="feature in features" :key="feature.id" class="text-center group">
          <div
            class="bg-white rounded-lg p-8 shadow-md hover:shadow-xl transition-all duration-300 transform group-hover:-translate-y-2"
          >
            <!-- 图标 -->
            <div
              class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary-200 transition-colors duration-300"
            >
              <span class="text-3xl">{{ feature.icon }}</span>
            </div>

            <!-- 标题 -->
            <h3 class="text-xl font-bold text-gray-900 mb-4">
              {{ feature.title }}
            </h3>

            <!-- 描述 -->
            <p class="text-gray-600 leading-relaxed">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="mt-16 bg-white rounded-lg shadow-lg p-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div v-for="stat in stats" :key="stat.id" class="text-center">
            <div class="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
              {{ stat.value }}
            </div>
            <div class="text-gray-600 font-medium">
              {{ stat.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  // 特色服务数据
  const features = [
    {
      id: 1,
      icon: '🎯',
      title: '专业定制',
      description: '根据您的需求和预算，为您量身定制专属的旅行方案，确保每一次出行都独一无二。',
    },
    {
      id: 2,
      icon: '🛡️',
      title: '安全保障',
      description: '全程旅游保险覆盖，专业导游陪同，24小时客服支持，让您的旅行安全无忧。',
    },
    {
      id: 3,
      icon: '💎',
      title: '品质服务',
      description: '精选优质酒店，专业交通安排，地道美食体验，为您提供高品质的旅行服务。',
    },
    {
      id: 4,
      icon: '💰',
      title: '价格透明',
      description: '无隐藏费用，价格公开透明，提供最具性价比的旅游产品，让您物超所值。',
    },
  ];

  // 统计数据
  const stats = [
    {
      id: 1,
      value: '10万+',
      label: '满意客户',
    },
    {
      id: 2,
      value: '500+',
      label: '旅游线路',
    },
    {
      id: 3,
      value: '50+',
      label: '合作城市',
    },
    {
      id: 4,
      value: '98%',
      label: '好评率',
    },
  ];
</script>

<style scoped>
  /* 自定义样式 */
</style>
