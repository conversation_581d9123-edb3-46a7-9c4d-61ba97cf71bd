<template>
  <header class="bg-white shadow-md sticky top-0 z-50">
    <!-- 顶部信息栏 -->
    <div class="bg-primary-700 text-white py-2">
      <div class="container-custom flex justify-between items-center text-sm">
        <div class="flex items-center space-x-4">
          <span>📞 400-123-4567</span>
          <span>✉️ <EMAIL></span>
        </div>
        <div class="flex items-center space-x-4">
          <span>🌐 中文</span>
          <span>💰 CNY</span>
        </div>
      </div>
    </div>

    <!-- 主导航栏 -->
    <div class="container-custom py-4">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <div class="flex items-center">
          <NuxtLink to="/" class="flex items-center space-x-2">
            <div
              class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold text-xl"
            >
              T
            </div>
            <span class="text-2xl font-bold text-gray-800">Travel</span>
          </NuxtLink>
        </div>

        <!-- 桌面端导航菜单 -->
        <nav class="hidden lg:flex items-center space-x-8">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group"
          >
            {{ item.name }}
            <div
              class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full"
            />
          </NuxtLink>
        </nav>

        <!-- 搜索和操作按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 搜索框 -->
          <div class="hidden md:block relative">
            <el-input v-model="searchQuery" placeholder="搜索目的地..." class="w-64" clearable>
              <template #prefix>
                <span class="text-gray-400">🔍</span>
              </template>
            </el-input>
          </div>

          <!-- 移动端搜索按钮 -->
          <button class="md:hidden p-2 text-gray-600 hover:text-primary-600">🔍</button>

          <!-- 收藏夹 -->
          <button class="p-2 text-gray-600 hover:text-primary-600 relative">
            ❤️
            <span
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {{ favoriteCount }}
            </span>
          </button>

          <!-- 购物车 -->
          <button class="p-2 text-gray-600 hover:text-primary-600 relative">
            🛒
            <span
              class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {{ cartCount }}
            </span>
          </button>

          <!-- 移动端菜单按钮 -->
          <button class="lg:hidden p-2 text-gray-600 hover:text-primary-600" @click="toggleMobileMenu">
            <div class="w-6 h-6 flex flex-col justify-center space-y-1">
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ 'rotate-45 translate-y-1.5': mobileMenuOpen }"
              />
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ 'opacity-0': mobileMenuOpen }"
              />
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ '-rotate-45 -translate-y-1.5': mobileMenuOpen }"
              />
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div v-show="mobileMenuOpen" class="lg:hidden bg-white border-t border-gray-200 py-4">
      <div class="container-custom">
        <!-- 移动端搜索框 -->
        <div class="mb-4">
          <el-input v-model="searchQuery" placeholder="搜索目的地..." clearable>
            <template #prefix>
              <span class="text-gray-400">🔍</span>
            </template>
          </el-input>
        </div>

        <!-- 移动端导航菜单 -->
        <nav class="space-y-2">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="block py-2 text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
            @click="closeMobileMenu"
          >
            {{ item.name }}
          </NuxtLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
  // 响应式数据
  const searchQuery = ref('');
  const mobileMenuOpen = ref(false);
  const favoriteCount = ref(3);
  const cartCount = ref(0);

  // 导航菜单项
  const menuItems = [
    { name: '首页', path: '/' },
    { name: '目的地', path: '/destinations' },
    { name: '旅游产品', path: '/tours' },
    { name: '酒店', path: '/hotels' },
    { name: '攻略', path: '/guides' },
    { name: '关于我们', path: '/about' },
    { name: '联系我们', path: '/contact' },
  ];

  // 方法
  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
  };

  const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
  };

  // 监听路由变化，关闭移动端菜单
  const route = useRoute();
  watch(
    () => route.path,
    () => {
      closeMobileMenu();
    }
  );
</script>

<style scoped>
  /* 自定义样式 */
  .router-link-active {
    color: #0284c7;
  }

  /* Element Plus 输入框自定义样式 */
  :deep(.el-input__wrapper) {
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
  }

  :deep(.el-input__wrapper:hover) {
    border-color: #0284c7;
  }

  :deep(.el-input__wrapper.is-focus) {
    border-color: #0284c7;
    box-shadow: 0 0 0 2px rgba(2, 132, 199, 0.1);
  }
</style>
