<template>
  <header class="bg-white shadow-md sticky top-0 z-50">
    <!-- Top info bar -->
    <div class="bg-primary-700 text-white py-2">
      <div class="container-custom flex justify-between items-center text-sm">
        <div class="flex items-center space-x-4">
          <span>📞 +1-800-123-4567</span>
          <span>✉️ <EMAIL></span>
        </div>
        <div class="flex items-center space-x-4">
          <span>🌐 English</span>
          <span>💰 USD</span>
        </div>
      </div>
    </div>

    <!-- Main navigation bar -->
    <div class="container-custom py-4">
      <div class="flex items-center justify-between">
        <!-- Logo -->
        <div class="flex items-center">
          <NuxtLink to="/" class="flex items-center space-x-2">
            <div
              class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold text-xl"
            >
              T
            </div>
            <span class="text-2xl font-bold text-gray-800">Travel</span>
          </NuxtLink>
        </div>

        <!-- Desktop navigation menu -->
        <nav class="hidden lg:flex items-center space-x-8">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group"
          >
            {{ item.name }}
            <div
              class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full"
            />
          </NuxtLink>
        </nav>

        <!-- Search and action buttons -->
        <div class="flex items-center space-x-4">
          <!-- Search box -->
          <div class="hidden md:block relative">
            <el-input v-model="searchQuery" placeholder="Search destinations..." class="w-64" clearable>
              <template #prefix>
                <span class="text-gray-400">🔍</span>
              </template>
            </el-input>
          </div>

          <!-- Mobile search button -->
          <button class="md:hidden p-2 text-gray-600 hover:text-primary-600">🔍</button>

          <!-- Favorites -->
          <button class="p-2 text-gray-600 hover:text-primary-600 relative">
            ❤️
            <span
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {{ favoriteCount }}
            </span>
          </button>

          <!-- Shopping cart -->
          <button class="p-2 text-gray-600 hover:text-primary-600 relative">
            🛒
            <span
              class="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
            >
              {{ cartCount }}
            </span>
          </button>

          <!-- Mobile menu button -->
          <button class="lg:hidden p-2 text-gray-600 hover:text-primary-600" @click="toggleMobileMenu">
            <div class="w-6 h-6 flex flex-col justify-center space-y-1">
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ 'rotate-45 translate-y-1.5': mobileMenuOpen }"
              />
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ 'opacity-0': mobileMenuOpen }"
              />
              <div
                class="w-full h-0.5 bg-current transition-all duration-200"
                :class="{ '-rotate-45 -translate-y-1.5': mobileMenuOpen }"
              />
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile menu -->
    <div v-show="mobileMenuOpen" class="lg:hidden bg-white border-t border-gray-200 py-4">
      <div class="container-custom">
        <!-- Mobile search box -->
        <div class="mb-4">
          <el-input v-model="searchQuery" placeholder="Search destinations..." clearable>
            <template #prefix>
              <span class="text-gray-400">🔍</span>
            </template>
          </el-input>
        </div>

        <!-- Mobile navigation menu -->
        <nav class="space-y-2">
          <NuxtLink
            v-for="item in menuItems"
            :key="item.name"
            :to="item.path"
            class="block py-2 text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200"
            @click="closeMobileMenu"
          >
            {{ item.name }}
          </NuxtLink>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
  // Reactive data
  const searchQuery = ref('');
  const mobileMenuOpen = ref(false);
  const favoriteCount = ref(3);
  const cartCount = ref(0);

  // Navigation menu items
  const menuItems = [
    { name: 'Home', path: '/' },
    { name: 'Destinations', path: '/destinations' },
    { name: 'Tours', path: '/tours' },
    { name: 'Hotels', path: '/hotels' },
    { name: 'Guides', path: '/guides' },
    { name: 'About Us', path: '/about' },
    { name: 'Contact', path: '/contact' },
  ];

  // Methods
  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;
  };

  const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
  };

  // Watch route changes to close mobile menu
  const route = useRoute();
  watch(
    () => route.path,
    () => {
      closeMobileMenu();
    }
  );
</script>

<style scoped>
  /* Custom styles */
  .router-link-active {
    color: #0284c7;
  }

  /* Element Plus input custom styles */
  :deep(.el-input__wrapper) {
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
  }

  :deep(.el-input__wrapper:hover) {
    border-color: #0284c7;
  }

  :deep(.el-input__wrapper.is-focus) {
    border-color: #0284c7;
    box-shadow: 0 0 0 2px rgba(2, 132, 199, 0.1);
  }
</style>
