<template>
  <section class="relative">
    <el-carousel
      :height="carouselHeight"
      :interval="5000"
      :arrow="'hover'"
      :indicator-position="'outside'"
      class="hero-carousel"
    >
      <el-carousel-item v-for="(slide, index) in slides" :key="index">
        <div
          class="relative w-full h-full bg-cover bg-center bg-no-repeat"
          :style="{ backgroundImage: `url(${slide.image})` }"
        >
          <!-- 遮罩层 -->
          <div class="absolute inset-0 bg-black bg-opacity-40" />

          <!-- 内容区域 -->
          <div class="relative z-10 h-full flex items-center">
            <div class="container-custom">
              <div class="max-w-2xl text-white">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
                  {{ slide.title }}
                </h1>
                <p class="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed">
                  {{ slide.description }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                  <button class="btn-primary text-lg px-8 py-3">
                    {{ slide.primaryButton }}
                  </button>
                  <button
                    class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg transition-all duration-200"
                  >
                    {{ slide.secondaryButton }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- 搜索框区域 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4 z-20">
      <div class="bg-white rounded-lg shadow-xl p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- 目的地 -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">目的地</label>
            <el-select v-model="searchForm.destination" placeholder="选择目的地" class="w-full" filterable>
              <el-option v-for="dest in destinations" :key="dest.value" :label="dest.label" :value="dest.value" />
            </el-select>
          </div>

          <!-- 出发日期 -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">出发日期</label>
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              :disabled-date="disabledDate"
            />
          </div>

          <!-- 返回日期 -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">返回日期</label>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="选择日期"
              class="w-full"
              :disabled-date="disabledEndDate"
            />
          </div>

          <!-- 搜索按钮 -->
          <div class="flex items-end">
            <button class="w-full btn-primary py-3 text-lg font-medium" @click="handleSearch">🔍 搜索</button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus';

  // 响应式数据
  const carouselHeight = ref('600px');
  const searchForm = reactive({
    destination: '',
    startDate: '',
    endDate: '',
  });

  // 轮播图数据
  const slides = [
    {
      image:
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: '探索神秘的山川大地',
      description: '踏上一段难忘的旅程，发现大自然的壮丽景色和隐藏的宝藏',
      primaryButton: '立即预订',
      secondaryButton: '了解更多',
    },
    {
      image:
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: '体验古老文明的魅力',
      description: '走进历史悠久的古城，感受千年文化的深厚底蕴',
      primaryButton: '查看行程',
      secondaryButton: '获取报价',
    },
    {
      image:
        'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: '享受海滨度假时光',
      description: '在碧海蓝天下放松身心，享受完美的海滨假期',
      primaryButton: '预订酒店',
      secondaryButton: '查看详情',
    },
  ];

  // 目的地选项
  const destinations = [
    { label: '北京', value: 'beijing' },
    { label: '上海', value: 'shanghai' },
    { label: '西安', value: 'xian' },
    { label: '成都', value: 'chengdu' },
    { label: '杭州', value: 'hangzhou' },
    { label: '三亚', value: 'sanya' },
    { label: '丽江', value: 'lijiang' },
    { label: '桂林', value: 'guilin' },
  ];

  // 方法
  function disabledDate(time: Date) {
    return time.getTime() < Date.now() - 8.64e7;
  }

  function disabledEndDate(time: Date) {
    if (!searchForm.startDate) return time.getTime() < Date.now() - 8.64e7;
    return time.getTime() < new Date(searchForm.startDate).getTime();
  }

  const handleSearch = () => {
    if (!searchForm.destination) {
      ElMessage.warning('请选择目的地');
      return;
    }
    if (!searchForm.startDate) {
      ElMessage.warning('请选择出发日期');
      return;
    }

    // 执行搜索逻辑
    console.log('搜索参数:', searchForm);
    ElMessage.success('搜索中...');

    // 这里可以跳转到搜索结果页面
    // await navigateTo(`/search?destination=${searchForm.destination}&start=${searchForm.startDate}&end=${searchForm.endDate}`)
  };

  // 响应式高度调整
  onMounted(() => {
    const updateHeight = () => {
      if (window.innerWidth < 768) {
        carouselHeight.value = '500px';
      } else if (window.innerWidth < 1024) {
        carouselHeight.value = '550px';
      } else {
        carouselHeight.value = '600px';
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);

    onUnmounted(() => {
      window.removeEventListener('resize', updateHeight);
    });
  });
</script>

<style scoped>
  /* 轮播图自定义样式 */
  :deep(.el-carousel__container) {
    border-radius: 0;
  }

  :deep(.el-carousel__indicator) {
    background-color: rgba(255, 255, 255, 0.5);
  }

  :deep(.el-carousel__indicator.is-active) {
    background-color: white;
  }

  :deep(.el-carousel__arrow) {
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
  }

  :deep(.el-carousel__arrow:hover) {
    background-color: rgba(0, 0, 0, 0.7);
  }

  /* 搜索表单样式 */
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-date-editor) {
    width: 100%;
  }

  :deep(.el-input__wrapper) {
    border-radius: 0.5rem;
  }
</style>
