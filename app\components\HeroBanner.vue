<template>
  <section class="relative">
    <el-carousel
      :height="carouselHeight"
      :interval="5000"
      :arrow="'hover'"
      :indicator-position="'outside'"
      class="hero-carousel"
    >
      <el-carousel-item v-for="(slide, index) in slides" :key="index">
        <div
          class="relative w-full h-full bg-cover bg-center bg-no-repeat"
          :style="{ backgroundImage: `url(${slide.image})` }"
        >
          <!-- 遮罩层 -->
          <div class="absolute inset-0 bg-black bg-opacity-40" />

          <!-- 内容区域 -->
          <div class="relative z-10 h-full flex items-center">
            <div class="container-custom">
              <div class="max-w-2xl text-white">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
                  {{ slide.title }}
                </h1>
                <p class="text-lg md:text-xl mb-8 text-gray-200 leading-relaxed">
                  {{ slide.description }}
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                  <button class="btn-primary text-lg px-8 py-3">
                    {{ slide.primaryButton }}
                  </button>
                  <button
                    class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-3 rounded-lg transition-all duration-200"
                  >
                    {{ slide.secondaryButton }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>

    <!-- Search form area -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4 z-20">
      <div class="bg-white rounded-lg shadow-xl p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Destination -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">Destination</label>
            <el-select v-model="searchForm.destination" placeholder="Select destination" class="w-full" filterable>
              <el-option v-for="dest in destinations" :key="dest.value" :label="dest.label" :value="dest.value" />
            </el-select>
          </div>

          <!-- Departure date -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">Departure Date</label>
            <el-date-picker
              v-model="searchForm.startDate"
              type="date"
              placeholder="Select date"
              class="w-full"
              :disabled-date="disabledDate"
            />
          </div>

          <!-- Return date -->
          <div class="space-y-2">
            <label class="text-sm font-medium text-gray-700">Return Date</label>
            <el-date-picker
              v-model="searchForm.endDate"
              type="date"
              placeholder="Select date"
              class="w-full"
              :disabled-date="disabledEndDate"
            />
          </div>

          <!-- Search button -->
          <div class="flex items-end">
            <button class="w-full btn-primary py-3 text-lg font-medium" @click="handleSearch">🔍 Search</button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus';

  // Reactive data
  const carouselHeight = ref('600px');
  const searchForm = reactive({
    destination: '',
    startDate: '',
    endDate: '',
  });

  // Carousel data
  const slides = [
    {
      image:
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: 'Explore Mysterious Mountains and Landscapes',
      description:
        'Embark on an unforgettable journey to discover the magnificent scenery and hidden treasures of nature',
      primaryButton: 'Book Now',
      secondaryButton: 'Learn More',
    },
    {
      image:
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: 'Experience the Charm of Ancient Civilizations',
      description: 'Step into historic ancient cities and feel the profound heritage of thousand-year-old cultures',
      primaryButton: 'View Itinerary',
      secondaryButton: 'Get Quote',
    },
    {
      image:
        'https://images.unsplash.com/photo-1506197603052-3cc9c3a201bd?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80',
      title: 'Enjoy Seaside Vacation Time',
      description: 'Relax and unwind under the blue sea and sky, enjoying the perfect seaside holiday',
      primaryButton: 'Book Hotel',
      secondaryButton: 'View Details',
    },
  ];

  // Destination options
  const destinations = [
    { label: 'Beijing', value: 'beijing' },
    { label: 'Shanghai', value: 'shanghai' },
    { label: "Xi'an", value: 'xian' },
    { label: 'Chengdu', value: 'chengdu' },
    { label: 'Hangzhou', value: 'hangzhou' },
    { label: 'Sanya', value: 'sanya' },
    { label: 'Lijiang', value: 'lijiang' },
    { label: 'Guilin', value: 'guilin' },
  ];

  // Methods
  function disabledDate(time: Date) {
    return time.getTime() < Date.now() - 8.64e7;
  }

  function disabledEndDate(time: Date) {
    if (!searchForm.startDate) return time.getTime() < Date.now() - 8.64e7;
    return time.getTime() < new Date(searchForm.startDate).getTime();
  }

  const handleSearch = () => {
    if (!searchForm.destination) {
      ElMessage.warning('Please select a destination');
      return;
    }
    if (!searchForm.startDate) {
      ElMessage.warning('Please select a departure date');
      return;
    }

    // Execute search logic
    console.log('Search parameters:', searchForm);
    ElMessage.success('Searching...');

    // Navigate to search results page
    // await navigateTo(`/search?destination=${searchForm.destination}&start=${searchForm.startDate}&end=${searchForm.endDate}`)
  };

  // Responsive height adjustment
  onMounted(() => {
    const updateHeight = () => {
      if (window.innerWidth < 768) {
        carouselHeight.value = '500px';
      } else if (window.innerWidth < 1024) {
        carouselHeight.value = '550px';
      } else {
        carouselHeight.value = '600px';
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);

    onUnmounted(() => {
      window.removeEventListener('resize', updateHeight);
    });
  });
</script>

<style scoped>
  /* Custom carousel styles */
  :deep(.el-carousel__container) {
    border-radius: 0;
  }

  :deep(.el-carousel__indicator) {
    background-color: rgba(255, 255, 255, 0.5);
  }

  :deep(.el-carousel__indicator.is-active) {
    background-color: white;
  }

  :deep(.el-carousel__arrow) {
    background-color: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
  }

  :deep(.el-carousel__arrow:hover) {
    background-color: rgba(0, 0, 0, 0.7);
  }

  /* Search form styles */
  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-date-editor) {
    width: 100%;
  }

  :deep(.el-input__wrapper) {
    border-radius: 0.5rem;
  }
</style>
