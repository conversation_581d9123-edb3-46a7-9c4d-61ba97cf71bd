{"name": "travel-web", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prettier": "prettier --write .", "lint": "eslint . --fix"}, "dependencies": {"@nuxt/eslint": "1.9.0", "@nuxt/image": "1.11.0", "@unocss/reset": "^66.5.1", "dayjs": "^1.11.18", "eslint": "^9.0.0", "lodash-unified": "^1.0.3", "nuxt": "^4.1.1", "vue": "^3.5.21", "vue-router": "^4.5.1"}, "devDependencies": {"@element-plus/nuxt": "^1.1.4", "@unocss/nuxt": "^66.5.1", "element-plus": "^2.11.2", "prettier": "^3.6.2", "unocss": "^66.5.1"}}